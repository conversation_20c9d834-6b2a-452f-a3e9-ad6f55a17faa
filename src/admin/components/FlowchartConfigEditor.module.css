/* FlowchartConfigEditor Styles */

.container {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 16px;
  background-color: #f9fafb;
}

.header {
  margin-bottom: 16px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.instructions {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 14px;
  color: #1e40af;
}

.instructionsTitle {
  font-weight: 600;
}

.instructionsList {
  margin-top: 4px;
  margin-left: 16px;
  list-style-type: disc;
}

.buttonContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.addButton {
  padding: 8px 12px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #2563eb;
}

.clearButton {
  padding: 8px 12px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background-color: #dc2626;
}

.flowContainer {
  height: 400px;
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
}

.stats {
  margin-top: 12px;
  font-size: 12px;
  color: #6b7280;
}

/* Node Styles */
.nodeContainer {
  padding: 16px;
  border: 2px solid #93c5fd;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  position: relative;
}

.nodeContainer:hover .deleteButton {
  opacity: 1;
}

.deleteButton {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nodeInput {
  width: 100%;
  padding: 8px;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.nodeInput:focus {
  border-color: #3b82f6;
}

.nodeLabel {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  color: gray;
}

.nodeLabel:hover {
  background-color: #eff6ff;
}

.nodeHandle {
  width: 12px;
  height: 12px;
  background-color: #3b82f6;
}

/* Edge Styles */
.edgePath {
  stroke: #6366f1;
  stroke-width: 2;
  fill: none;
  marker-end: url(#arrowhead);
}

.edgeLabel {
  padding: 4px 8px;
  font-size: 12px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.2s;
  color: gray;
}

.edgeLabel:hover {
  background-color: #eff6ff;
}

.edgeInput {
  width: 100%;
  padding: 2px 4px;
  font-size: 12px;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  background-color: white;
  outline: none;
}

.edgeInputContainer {
  overflow: visible;
}

/* Controls */
.controls {
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Background - ReactFlow Background component doesn't use className for color */
