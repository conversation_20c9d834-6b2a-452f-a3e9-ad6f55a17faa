/* FlowchartConfigEditor Styles */

.container {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 16px;
  background-color: #f9fafb;
}

.header {
  margin-bottom: 16px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.instructions {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 14px;
  color: #1e40af;
}

.instructionsTitle {
  font-weight: 600;
}

.instructionsList {
  margin-top: 4px;
  margin-left: 16px;
  list-style-type: disc;
}

.buttonContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.addButton {
  padding: 8px 12px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #2563eb;
}

.addInnovationButton {
  padding: 8px 12px;
  background-color: #a855f7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addInnovationButton:hover {
  background-color: #9333ea;
}

.clearButton {
  padding: 8px 12px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background-color: #dc2626;
}

.flowContainer {
  height: 400px;
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
}

.stats {
  margin-top: 12px;
  font-size: 12px;
  color: #6b7280;
}

/* Node Styles */
.nodeContainer {
  padding: 16px;
  border: 2px solid #93c5fd;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  position: relative;
}

.nodeContainer.innovation {
  border: 2px solid #a855f7;
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border-radius: 12px;
}

.nodeContainer:hover .deleteButton {
  opacity: 1;
}

.deleteButton {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nodeInput {
  width: 100%;
  padding: 8px;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.nodeInput:focus {
  border-color: #3b82f6;
}

.nodeLabel {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  color: gray;
}

.nodeLabel:hover {
  background-color: #eff6ff;
}

.nodeHandle {
  width: 12px;
  height: 12px;
  background-color: #3b82f6;
}

.innovation .nodeHandle {
  background-color: #a855f7;
  width: 14px;
  height: 14px;
}

/* Edge Styles */
.edgePath {
  stroke: #6366f1;
  stroke-width: 2;
  fill: none;
  marker-end: url(#arrowhead);
}

.innovationEdgePath {
  stroke: #a855f7;
  stroke-width: 3;
  fill: none;
  marker-end: url(#innovationArrowhead);
  stroke-dasharray: 5, 5;
}

.edgeLabel {
  padding: 4px 8px;
  font-size: 12px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.2s;
  color: gray;
  z-index: 10;
}

.edgeLabel:hover {
  background-color: #eff6ff;
}

.innovationEdgeLabel {
  padding: 4px 8px;
  font-size: 12px;
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border: 2px solid #a855f7;
  border-radius: 6px;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.2s;
  font-weight: 600;
  color: #7c3aed;
}

.innovationEdgeLabel:hover {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
}

.edgeInput {
  width: 100%;
  padding: 2px 4px;
  font-size: 12px;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  outline: none;
}

.edgeInputContainer {
  overflow: visible;
}

.edgeDeleteButton {
  width: 18px;
  height: 18px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  z-index: 100;
}

.edgeDeleteButton:hover {
  background-color: #dc2626;
}

.edgeInsertButton {
  width: 18px;
  height: 18px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  font-weight: bold;
}

.edgeInsertButton:hover {
  background-color: #059669;
}

/* Controls */
.controls {
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Background - ReactFlow Background component doesn't use className for color */
