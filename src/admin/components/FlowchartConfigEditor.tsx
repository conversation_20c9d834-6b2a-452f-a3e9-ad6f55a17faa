'use client';
import { useField } from '@payloadcms/ui';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import React<PERSON>low, {
  addEdge,
  Background,
  Connection,
  Controls,
  Handle,
  Position,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';
import styles from './FlowchartConfigEditor.module.css';

// Custom node component with editable labels
const EditableNode = ({ data, id }: { data: any; id: string }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data.label);

  useEffect(() => {
    setEditValue(data.label);
  }, [data.label]);

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    if (editValue.trim()) {
      data.onUpdate(id, { label: editValue.trim() });
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(data.label);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  };

  const handleDelete = () => {
    if (confirm('Delete this node?')) {
      data.onDelete(id);
    }
  };

  const nodeClassName =
    data.type === 'Innovation'
      ? `${styles.nodeContainer} ${styles.innovation}`
      : styles.nodeContainer;

  return (
    <div className={nodeClassName}>
      <button onClick={handleDelete} className={styles.deleteButton}>
        ×
      </button>

      {isEditing ? (
        <input
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onBlur={handleSave}
          onKeyDown={handleKeyPress}
          className={styles.nodeInput}
          autoFocus
        />
      ) : (
        <div onClick={handleEdit} className={styles.nodeLabel} title="Click to edit">
          {data.label}
        </div>
      )}

      {/* Render handles based on node type */}
      {data.type === 'Innovation' ? (
        <>
          {/* Innovation Class nodes have left and right handles */}
          <Handle type="target" position={Position.Left} className={styles.nodeHandle} />
          <Handle type="source" position={Position.Right} className={styles.nodeHandle} />
        </>
      ) : (
        <>
          {/* Regular nodes have top and bottom handles */}
          <Handle type="source" position={Position.Bottom} className={styles.nodeHandle} />
          <Handle type="target" position={Position.Top} className={styles.nodeHandle} />
        </>
      )}
    </div>
  );
};

// Custom edge component with editable labels
const EditableEdge = ({ id, sourceX, sourceY, targetX, targetY, data }: any) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data?.label || '');
  const [isHovered, setIsHovered] = useState(false);

  const edgePath = `M${sourceX},${sourceY} L${targetX},${targetY}`;
  const labelX = (sourceX + targetX) / 2;
  const labelY = (sourceY + targetY) / 2;

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    data.onUpdate(id, { label: editValue.trim() });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(data?.label || '');
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  };

  const handleDelete = () => {
    if (confirm('Delete this connection?')) {
      data.onDelete(id);
    }
  };

  const handleInsertInnovationClass = () => {
    if (data.onInsertInnovationClass) {
      data.onInsertInnovationClass(id, labelX, labelY);
    }
  };

  return (
    <g onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <path id={id} d={edgePath} className={styles.edgePath} />

      {/* Action buttons - positioned near the middle of the edge */}
      {isHovered && !isEditing && (
        <>
          {/* Insert Innovation Class button */}
          <foreignObject
            x={labelX + 20}
            y={labelY - 22}
            width={20}
            height={20}
            className={styles.edgeInputContainer}
            requiredExtensions="http://www.w3.org/1999/xhtml"
          >
            <div style={{ position: 'relative', pointerEvents: 'auto' }}>
              <button
                onClick={handleInsertInnovationClass}
                className={styles.edgeInsertButton}
                title="Insert Innovation Class"
                style={{ zIndex: 1000 }}
              >
                +
              </button>
            </div>
          </foreignObject>

          {/* Delete button */}
          <foreignObject
            x={labelX + 45}
            y={labelY - 19}
            width={20}
            height={20}
            className={styles.edgeInputContainer}
            requiredExtensions="http://www.w3.org/1999/xhtml"
          >
            <div style={{ position: 'relative', pointerEvents: 'auto' }}>
              <button
                onClick={handleDelete}
                className={styles.edgeDeleteButton}
                title="Delete connection"
                style={{ zIndex: 1000 }}
              >
                ×
              </button>
            </div>
          </foreignObject>
        </>
      )}

      {/* Label/Input area */}
      <foreignObject
        x={labelX - 50}
        y={labelY - 10}
        width={100}
        height={20}
        className={styles.edgeInputContainer}
      >
        {isEditing ? (
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyPress}
            className={styles.edgeInput}
            autoFocus
          />
        ) : (
          <div onClick={handleEdit} className={styles.edgeLabel} title="Click to edit edge label">
            {data?.label || 'Click to name'}
          </div>
        )}
      </foreignObject>
    </g>
  );
};

const nodeTypes = {
  editable: EditableNode,
};

const edgeTypes = {
  editable: EditableEdge,
};

const FlowchartConfigEditorComponent: React.FC<{ path: string; name: string }> = () => {
  const { value, setValue } = useField<any>({ path: 'flowchartConfig' });

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const updateFieldValueRef = useRef<((updatedNodes?: any[], updatedEdges?: any[]) => void) | null>(
    null,
  );

  // Update field value with current nodes and edges
  const updateFieldValue = useCallback(
    (updatedNodes?: any[], updatedEdges?: any[]) => {
      const currentNodes = updatedNodes || nodes;
      const currentEdges = updatedEdges || edges;

      const config = {
        nodes: currentNodes.map((node) => ({
          id: node.id,
          label: node.data.label,
          position: node.position,
        })),
        edges: currentEdges.map((edge) => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.data?.label || '',
        })),
      };

      console.log('Saving flowchart config:', config);
      setValue(config);
    },
    [nodes, edges, setValue],
  );

  updateFieldValueRef.current = updateFieldValue;

  // Handle node position changes (when dragged)
  useEffect(() => {
    if (isInitialized) {
      updateFieldValue();
    }
  }, [nodes, edges, isInitialized, updateFieldValue]);

  // Handle node updates
  const handleNodeUpdate = useCallback(
    (nodeId: string, updates: { label?: string }) => {
      setNodes((nds) => {
        const updatedNodes = nds.map((node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node,
        );
        // Update field value immediately with the new nodes
        updateFieldValue(updatedNodes, edges);
        return updatedNodes;
      });
    },
    [setNodes, updateFieldValue, edges],
  );

  // Handle node deletion
  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => node.id !== nodeId);
        setEdges((eds) => {
          const updatedEdges = eds.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId,
          );
          // Update field value immediately with both updated nodes and edges
          updateFieldValue(updatedNodes, updatedEdges);
          return updatedEdges;
        });
        return updatedNodes;
      });
    },
    [setNodes, setEdges, updateFieldValue],
  );

  // Handle edge updates
  const handleEdgeUpdate = useCallback(
    (edgeId: string, updates: { label?: string }) => {
      setEdges((eds) => {
        const updatedEdges = eds.map((edge) =>
          edge.id === edgeId ? { ...edge, data: { ...edge.data, ...updates } } : edge,
        );
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [setEdges, updateFieldValue, nodes],
  );

  // Handle edge deletion
  const handleEdgeDelete = useCallback(
    (edgeId: string) => {
      setEdges((eds) => {
        const updatedEdges = eds.filter((edge) => edge.id !== edgeId);
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [setEdges, updateFieldValue, nodes],
  );

  // Handle inserting innovation class between two nodes
  const handleInsertInnovationClass = useCallback(
    (edgeId: string, x: number, y: number) => {
      // Find the edge to be replaced
      const edgeToReplace = edges.find((edge) => edge.id === edgeId);
      if (!edgeToReplace) return;

      // Create new innovation class node
      const newNodeId = `innovation-class-${Date.now()}`;
      const newNode = {
        id: newNodeId,
        type: 'editable',
        data: {
          label: 'Innovation Class',
          type: 'Innovation',
          onUpdate: handleNodeUpdate,
          onDelete: handleNodeDelete,
        },
        position: { x: x - 60, y: y + 30 }, // Position near the edge
      };

      // Create new edges: source -> new node -> target
      const firstEdgeId = `edge-${Date.now()}-1`;
      const secondEdgeId = `edge-${Date.now()}-2`;

      const firstEdge = {
        id: firstEdgeId,
        source: edgeToReplace.source,
        target: newNodeId,
        type: 'editable',
        data: {
          label: edgeToReplace.data?.label || '',
          onUpdate: handleEdgeUpdate,
          onDelete: handleEdgeDelete,
          onInsertInnovationClass: handleInsertInnovationClass,
        },
      };

      const secondEdge = {
        id: secondEdgeId,
        source: newNodeId,
        target: edgeToReplace.target,
        type: 'editable',
        data: {
          label: '',
          onUpdate: handleEdgeUpdate,
          onDelete: handleEdgeDelete,
          onInsertInnovationClass: handleInsertInnovationClass,
        },
      };

      // Update nodes and edges
      setNodes((nds) => {
        const updatedNodes = [...nds, newNode];
        setEdges((eds) => {
          const updatedEdges = eds
            .filter((edge) => edge.id !== edgeId) // Remove original edge
            .concat([firstEdge, secondEdge]); // Add new edges

          // Update field value immediately
          updateFieldValue(updatedNodes, updatedEdges);
          return updatedEdges;
        });
        return updatedNodes;
      });
    },
    [
      edges,
      handleNodeUpdate,
      handleNodeDelete,
      handleEdgeUpdate,
      handleEdgeDelete,
      setNodes,
      setEdges,
      updateFieldValue,
    ],
  );

  // Initialize from existing data
  useEffect(() => {
    if (isInitialized || !value) return;

    console.log('Initializing flowchart config:', value);

    const stableHandleNodeUpdate = (nodeId: string, updates: { label?: string }) => {
      setNodes((nds) => {
        const updatedNodes = nds.map((node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node,
        );
        // Update field value immediately
        if (updateFieldValueRef.current) {
          setEdges((currentEdges) => {
            updateFieldValueRef.current!(updatedNodes, currentEdges);
            return currentEdges;
          });
        }
        return updatedNodes;
      });
    };

    const stableHandleNodeDelete = (nodeId: string) => {
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => node.id !== nodeId);
        setEdges((eds) => {
          const updatedEdges = eds.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId,
          );
          // Update field value immediately
          if (updateFieldValueRef.current) {
            updateFieldValueRef.current!(updatedNodes, updatedEdges);
          }
          return updatedEdges;
        });
        return updatedNodes;
      });
    };

    const stableHandleEdgeUpdate = (edgeId: string, updates: { label?: string }) => {
      setEdges((eds) => {
        const updatedEdges = eds.map((edge) =>
          edge.id === edgeId ? { ...edge, data: { ...edge.data, ...updates } } : edge,
        );
        // Update field value immediately
        if (updateFieldValueRef.current) {
          setNodes((currentNodes) => {
            updateFieldValueRef.current!(currentNodes, updatedEdges);
            return currentNodes;
          });
        }
        return updatedEdges;
      });
    };

    const stableHandleEdgeDelete = (edgeId: string) => {
      setEdges((eds) => {
        const updatedEdges = eds.filter((edge) => edge.id !== edgeId);
        // Update field value immediately
        if (updateFieldValueRef.current) {
          setNodes((currentNodes) => {
            updateFieldValueRef.current!(currentNodes, updatedEdges);
            return currentNodes;
          });
        }
        return updatedEdges;
      });
    };

    const stableHandleInsertInnovationClass = (edgeId: string, x: number, y: number) => {
      // Use the main handler through ref to avoid stale closures
      if (handleInsertInnovationClass) {
        handleInsertInnovationClass(edgeId, x, y);
      }
    };

    if (value?.nodes?.length > 0) {
      const flowNodes = value.nodes.map((node: any) => ({
        id: node.id,
        type: 'editable',
        data: {
          label: node.label,
          onUpdate: stableHandleNodeUpdate,
          onDelete: stableHandleNodeDelete,
        },
        position: node.position || { x: Math.random() * 300, y: Math.random() * 200 },
      }));

      const flowEdges = (value.edges || []).map((edge: any) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: 'editable',
        data: {
          label: edge.label,
          onUpdate: stableHandleEdgeUpdate,
          onDelete: stableHandleEdgeDelete,
          onInsertInnovationClass: stableHandleInsertInnovationClass,
        },
      }));

      setNodes(flowNodes);
      setEdges(flowEdges);
    } else {
      // Create default nodes
      const defaultNodes = [
        {
          id: 'node-1',
          type: 'editable',
          data: {
            label: 'Start',
            onUpdate: stableHandleNodeUpdate,
            onDelete: stableHandleNodeDelete,
          },
          position: { x: 100, y: 100 },
        },
        {
          id: 'node-2',
          type: 'editable',
          data: {
            label: 'End',
            onUpdate: stableHandleNodeUpdate,
            onDelete: stableHandleNodeDelete,
          },
          position: { x: 300, y: 100 },
        },
      ];
      setNodes(defaultNodes);
    }

    setIsInitialized(true);
  }, [value, isInitialized]);

  // Handle new connections
  const onConnect = useCallback(
    (connection: Connection) => {
      const newEdge = {
        ...connection,
        id: `edge-${Date.now()}`,
        type: 'editable',
        data: {
          label: '',
          onUpdate: handleEdgeUpdate,
          onDelete: handleEdgeDelete,
          onInsertInnovationClass: handleInsertInnovationClass,
        },
      };
      setEdges((eds) => {
        const updatedEdges = addEdge(newEdge, eds);
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [
      setEdges,
      handleEdgeUpdate,
      handleEdgeDelete,
      handleInsertInnovationClass,
      updateFieldValue,
      nodes,
    ],
  );

  // Add new node
  const addNode = useCallback(
    (nodeType: string = 'Stage') => {
      const newId = `${nodeType.toLowerCase()}-${Date.now()}`;
      const newNode = {
        id: newId,
        type: 'editable',
        data: {
          label: nodeType === 'Innovation' ? 'Innovation Class' : 'New Node',
          type: nodeType,
          onUpdate: handleNodeUpdate,
          onDelete: handleNodeDelete,
        },
        position: { x: Math.random() * 400 + 50, y: Math.random() * 300 + 50 },
      };
      setNodes((nds) => {
        const updatedNodes = [...nds, newNode];
        // Update field value immediately with the new nodes
        updateFieldValue(updatedNodes, edges);
        return updatedNodes;
      });
    },
    [setNodes, handleNodeUpdate, handleNodeDelete, updateFieldValue, edges],
  );

  // Add regular node
  const addRegularNode = useCallback(() => addNode('Stage'), [addNode]);

  // Add innovation class node
  const addInnovationClassNode = useCallback(() => addNode('Innovation'), [addNode]);

  // Clear all
  const clearAll = useCallback(() => {
    if (confirm('Clear all nodes and edges?')) {
      setNodes([]);
      setEdges([]);
      setValue({ nodes: [], edges: [] });
    }
  }, [setNodes, setEdges, setValue]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3 className={styles.title}>Flowchart Configuration Editor</h3>

        <div className={styles.instructions}>
          <strong className={styles.instructionsTitle}>Instructions:</strong>
          <ul className={styles.instructionsList}>
            <li>Click on nodes to edit their names</li>
            <li>Click on edge labels to name connections</li>
            <li>Drag nodes to reposition them</li>
            <li>Connect nodes by dragging from one handle to another</li>
            <li>Hover over nodes to see delete button</li>
            <li>Hover over edges to see insert (+) and delete (×) buttons</li>
            <li>Click + button on edges to insert Innovation Class between nodes</li>
          </ul>
        </div>

        <div className={styles.buttonContainer}>
          <button type="button" onClick={addRegularNode} className={styles.addButton}>
            + Add Node
          </button>
          <button
            type="button"
            onClick={addInnovationClassNode}
            className={styles.addInnovationButton}
          >
            + Add Innovation Class
          </button>
          <button type="button" onClick={clearAll} className={styles.clearButton}>
            Clear All
          </button>
        </div>
      </div>

      <div className={styles.flowContainer}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          fitViewOptions={{ padding: 0.1 }}
          proOptions={{ hideAttribution: true }}
        >
          <Background color="#f1f5f9" gap={20} />
          <Controls className={styles.controls} />
          <svg>
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon points="0 0, 10 3.5, 0 7" fill="#6366f1" />
              </marker>
            </defs>
          </svg>
        </ReactFlow>
      </div>

      <div className={styles.stats}>
        <strong>Nodes:</strong> {nodes.length} | <strong>Edges:</strong> {edges.length}
      </div>
    </div>
  );
};

// Export wrapped in provider
const FlowchartConfigEditor = (props: { path: string; name: string }) => {
  return (
    <ReactFlowProvider>
      <FlowchartConfigEditorComponent {...props} />
    </ReactFlowProvider>
  );
};

export default FlowchartConfigEditor;
