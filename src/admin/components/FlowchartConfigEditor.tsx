'use client';
import { useField } from '@payloadcms/ui';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactFlow, {
  addEdge,
  Background,
  Connection,
  Controls,
  Handle,
  Position,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Custom node component with editable labels
const EditableNode = ({ data, id }: { data: any; id: string }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data.label);

  useEffect(() => {
    setEditValue(data.label);
  }, [data.label]);

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    if (editValue.trim()) {
      data.onUpdate(id, { label: editValue.trim() });
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(data.label);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  };

  const handleDelete = () => {
    if (confirm('Delete this node?')) {
      data.onDelete(id);
    }
  };

  return (
    <div className="p-4 border-2 border-blue-300 rounded-lg bg-white shadow-lg min-w-[120px] relative group">
      <button
        onClick={handleDelete}
        className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity"
      >
        ×
      </button>

      {isEditing ? (
        <input
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onBlur={handleSave}
          onKeyDown={handleKeyPress}
          className="w-full px-2 py-1 border border-blue-300 rounded text-sm focus:outline-none focus:border-blue-500"
          autoFocus
        />
      ) : (
        <div
          onClick={handleEdit}
          className="cursor-pointer hover:bg-blue-50 px-2 py-1 rounded text-sm font-medium"
          title="Click to edit"
        >
          {data.label}
        </div>
      )}

      <Handle type="source" position={Position.Bottom} className="w-3 h-3 bg-blue-500" />
      <Handle type="target" position={Position.Top} className="w-3 h-3 bg-blue-500" />
    </div>
  );
};

// Custom edge component with editable labels
const EditableEdge = ({ id, sourceX, sourceY, targetX, targetY, data }: any) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data?.label || '');

  const edgePath = `M${sourceX},${sourceY} L${targetX},${targetY}`;
  const labelX = (sourceX + targetX) / 2;
  const labelY = (sourceY + targetY) / 2;

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    data.onUpdate(id, { label: editValue.trim() });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(data?.label || '');
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  };

  return (
    <>
      <path
        id={id}
        d={edgePath}
        stroke="#6366f1"
        strokeWidth={2}
        fill="none"
        markerEnd="url(#arrowhead)"
      />
      <foreignObject
        x={labelX - 50}
        y={labelY - 10}
        width={100}
        height={20}
        className="overflow-visible"
      >
        {isEditing ? (
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyPress}
            className="w-full px-1 py-0.5 text-xs border border-blue-300 rounded bg-white focus:outline-none"
            autoFocus
          />
        ) : (
          <div
            onClick={handleEdit}
            className="px-2 py-1 text-xs bg-white border border-gray-300 rounded cursor-pointer hover:bg-blue-50 text-center"
            title="Click to edit edge label"
          >
            {data?.label || 'Click to name'}
          </div>
        )}
      </foreignObject>
    </>
  );
};

const nodeTypes = {
  editable: EditableNode,
};

const edgeTypes = {
  editable: EditableEdge,
};

const FlowchartConfigEditorComponent: React.FC<{ path: string; name: string }> = () => {
  const { value, setValue } = useField<any>({ path: 'flowchartConfig' });

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const updateFieldValueRef = useRef<() => void>(null);

  // Update field value with current nodes and edges
  const updateFieldValue = useCallback(() => {
    const config = {
      nodes: nodes.map((node) => ({
        id: node.id,
        label: node.data.label,
        position: node.position,
      })),
      edges: edges.map((edge) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        label: edge.data?.label || '',
      })),
    };

    console.log('Saving flowchart config:', config);
    setValue(config);
  }, [nodes, edges, setValue]);

  updateFieldValueRef.current = updateFieldValue;

  // Handle node updates
  const handleNodeUpdate = useCallback(
    (nodeId: string, updates: { label?: string }) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node,
        ),
      );
      setTimeout(() => updateFieldValue(), 0);
    },
    [setNodes, updateFieldValue],
  );

  // Handle node deletion
  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
      setTimeout(() => updateFieldValue(), 0);
    },
    [setNodes, setEdges, updateFieldValue],
  );

  // Handle edge updates
  const handleEdgeUpdate = useCallback(
    (edgeId: string, updates: { label?: string }) => {
      setEdges((eds) =>
        eds.map((edge) =>
          edge.id === edgeId ? { ...edge, data: { ...edge.data, ...updates } } : edge,
        ),
      );
      setTimeout(() => updateFieldValue(), 0);
    },
    [setEdges, updateFieldValue],
  );

  // Initialize from existing data
  useEffect(() => {
    if (isInitialized || !value) return;

    console.log('Initializing flowchart config:', value);

    const stableHandleNodeUpdate = (nodeId: string, updates: { label?: string }) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node,
        ),
      );
      setTimeout(() => {
        if (updateFieldValueRef.current) {
          updateFieldValueRef.current();
        }
      }, 0);
    };

    const stableHandleNodeDelete = (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
      setTimeout(() => {
        if (updateFieldValueRef.current) {
          updateFieldValueRef.current();
        }
      }, 0);
    };

    const stableHandleEdgeUpdate = (edgeId: string, updates: { label?: string }) => {
      setEdges((eds) =>
        eds.map((edge) =>
          edge.id === edgeId ? { ...edge, data: { ...edge.data, ...updates } } : edge,
        ),
      );
      setTimeout(() => {
        if (updateFieldValueRef.current) {
          updateFieldValueRef.current();
        }
      }, 0);
    };

    if (value?.nodes?.length > 0) {
      const flowNodes = value.nodes.map((node: any) => ({
        id: node.id,
        type: 'editable',
        data: {
          label: node.label,
          onUpdate: stableHandleNodeUpdate,
          onDelete: stableHandleNodeDelete,
        },
        position: node.position || { x: Math.random() * 300, y: Math.random() * 200 },
      }));

      const flowEdges = (value.edges || []).map((edge: any) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: 'editable',
        data: {
          label: edge.label,
          onUpdate: stableHandleEdgeUpdate,
        },
      }));

      setNodes(flowNodes);
      setEdges(flowEdges);
    } else {
      // Create default nodes
      const defaultNodes = [
        {
          id: 'node-1',
          type: 'editable',
          data: {
            label: 'Start',
            onUpdate: stableHandleNodeUpdate,
            onDelete: stableHandleNodeDelete,
          },
          position: { x: 100, y: 100 },
        },
        {
          id: 'node-2',
          type: 'editable',
          data: {
            label: 'End',
            onUpdate: stableHandleNodeUpdate,
            onDelete: stableHandleNodeDelete,
          },
          position: { x: 300, y: 100 },
        },
      ];
      setNodes(defaultNodes);
    }

    setIsInitialized(true);
  }, [value, isInitialized]);

  // Handle new connections
  const onConnect = useCallback(
    (connection: Connection) => {
      const newEdge = {
        ...connection,
        id: `edge-${Date.now()}`,
        type: 'editable',
        data: {
          label: '',
          onUpdate: handleEdgeUpdate,
        },
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setTimeout(() => updateFieldValue(), 0);
    },
    [setEdges, handleEdgeUpdate, updateFieldValue],
  );

  // Add new node
  const addNode = useCallback(() => {
    const newId = `node-${Date.now()}`;
    const newNode = {
      id: newId,
      type: 'editable',
      data: {
        label: 'New Node',
        onUpdate: handleNodeUpdate,
        onDelete: handleNodeDelete,
      },
      position: { x: Math.random() * 400 + 50, y: Math.random() * 300 + 50 },
    };
    setNodes((nds) => [...nds, newNode]);
    setTimeout(() => updateFieldValue(), 0);
  }, [setNodes, handleNodeUpdate, handleNodeDelete, updateFieldValue]);

  // Clear all
  const clearAll = useCallback(() => {
    if (confirm('Clear all nodes and edges?')) {
      setNodes([]);
      setEdges([]);
      setValue({ nodes: [], edges: [] });
    }
  }, [setNodes, setEdges, setValue]);

  return (
    <div className="border rounded-md p-4 bg-gray-50">
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Flowchart Configuration Editor</h3>

        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
          <strong>Instructions:</strong>
          <ul className="mt-1 ml-4 list-disc">
            <li>Click on nodes to edit their names</li>
            <li>Click on edge labels to name connections</li>
            <li>Drag nodes to reposition them</li>
            <li>Connect nodes by dragging from one handle to another</li>
            <li>Hover over nodes to see delete button</li>
          </ul>
        </div>

        <div className="flex gap-2 mb-4">
          <button
            type="button"
            onClick={addNode}
            className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            + Add Node
          </button>
          <button
            type="button"
            onClick={clearAll}
            className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Clear All
          </button>
        </div>
      </div>

      <div
        style={{ height: 400, width: '100%' }}
        className="border border-gray-300 rounded bg-white"
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          fitViewOptions={{ padding: 0.1 }}
        >
          <Background color="#f1f5f9" gap={20} />
          <Controls className="bg-white shadow-md" />
          <svg>
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon points="0 0, 10 3.5, 0 7" fill="#6366f1" />
              </marker>
            </defs>
          </svg>
        </ReactFlow>
      </div>

      <div className="mt-3 text-xs text-gray-600">
        <strong>Nodes:</strong> {nodes.length} | <strong>Edges:</strong> {edges.length}
      </div>
    </div>
  );
};

// Export wrapped in provider
const FlowchartConfigEditor = (props: { path: string; name: string }) => {
  return (
    <ReactFlowProvider>
      <FlowchartConfigEditorComponent {...props} />
    </ReactFlowProvider>
  );
};

export default FlowchartConfigEditor;
