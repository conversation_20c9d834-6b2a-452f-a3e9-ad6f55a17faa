import FlowchartConfigEditor from '@/admin/components/FlowchartConfigEditor';
import { CollectionConfig } from 'payload';

const FlowchartDemo: CollectionConfig = {
  slug: 'flowchart-demo',
  admin: {
    useAsTitle: 'name',
    description: 'Demo collection for flowchart configuration.',
    group: 'Demo',
  },
  labels: {
    singular: 'Flowchart Demo',
    plural: 'Flowchart Demos',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      admin: {
        description: 'Name for this flowchart configuration',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Description of this flowchart',
      },
    },
    {
      name: 'flowchartConfigEditor',
      type: 'ui',
      admin: {
        components: {
          Field: FlowchartConfigEditor as any,
        },
      },
      label: 'Flowchart Editor',
    },
    {
      name: 'flowchartConfig',
      type: 'json',
      label: 'Flowchart Configuration',
      admin: {
        description: 'Stores the complete flowchart configuration including nodes and named edges',
        readOnly: true,
      },
    },
  ],
};

export default FlowchartDemo;
